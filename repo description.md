I'll analyze your project structure and provide you with a comprehensive overview. Let me start by examining the codebase structure.

Now let me check if there are any packages directories and examine the environment setup:

Based on my analysis of your project structure, here's a comprehensive overview of your i-stethoscope project:

## 1. Project Organization Overview

Your project is a **monorepo** built with the Better T3 Stack template, organized using **Turborepo** for efficient build orchestration. Here's the structure:

```` path=README.md mode=EXCERPT
i-stethoscope/
├── apps/
│   ├── web/         # Frontend application (Next.js)
│   ├── native/      # Mobile application (React Native, Expo)
│   └── server/      # Backend API (Next, TRPC)
````

### Directory Breakdown:

- **Root Level**: Contains monorepo configuration files
  - `package.json` - Workspace configuration and shared scripts
  - `pnpm-workspace.yaml` - Defines workspace packages
  - `turbo.json` - Turborepo task pipeline configuration
  - `bts.jsonc` - Better T3 Stack configuration metadata

- **`apps/web/`**: Next.js frontend application (runs on port 3001)
  - Full-stack React app with shadcn/ui components
  - Includes authentication, routing, and tRPC client setup
  - Uses Tailwind CSS for styling

- **`apps/server/`**: Next.js backend API (runs on port 3000)
  - Houses the tRPC API routes and database logic
  - Contains Drizzle ORM schema and migrations
  - Handles authentication with Better Auth

- **`apps/native/`**: React Native mobile app with Expo
  - Cross-platform mobile application
  - Uses NativeWind for Tailwind-like styling
  - Shares tRPC types and authentication with web app

## 2. Node Modules Explanation

You have **3 separate `node_modules` directories** because this is a **pnpm workspace monorepo**:

1. **Root `node_modules/`**: Contains shared dependencies and workspace tools like Turborepo
2. **`apps/web/node_modules/`**: Web-specific dependencies (Next.js, React DOM, web-only packages)
3. **`apps/server/node_modules/`**: Server-specific dependencies (Drizzle Kit, database drivers, server-only packages)

This setup provides several benefits:
- **Dependency isolation**: Each app only gets the dependencies it needs
- **Efficient storage**: pnpm uses hard links to avoid duplication
- **Version flexibility**: Different apps can use different versions of the same package if needed
- **Faster installs**: Only changed dependencies need to be reinstalled

## 3. Common Development Workflows

### Starting Development Servers

````json path=package.json mode=EXCERPT
"scripts": {
  "dev": "turbo dev",
  "dev:native": "turbo -F native dev",
  "dev:web": "turbo -F web dev",
  "dev:server": "turbo -F server dev"
}
````

**Start all services simultaneously:**
```bash
pnpm dev
```

**Start individual services:**
```bash
# Web frontend only (port 3001)
pnpm dev:web

# Backend API only (port 3000)
pnpm dev:server

# Mobile app only
pnpm dev:native
```

### Database Management with Drizzle

````json path=package.json mode=EXCERPT
"db:push": "turbo -F server db:push",
"db:studio": "turbo -F server db:studio",
"db:generate": "turbo -F server db:generate",
"db:migrate": "turbo -F server db:migrate"
````

```bash
# Push schema changes to database (development)
pnpm db:push

# Generate migration files
pnpm db:generate

# Run migrations (production)
pnpm db:migrate

# Open Drizzle Studio (database GUI)
pnpm db:studio
```

### Adding Dependencies

**To web app:**
```bash
cd apps/web
pnpm add package-name
```

**To server:**
```bash
cd apps/server
pnpm add package-name
```

**To native app:**
```bash
cd apps/native
pnpm add package-name
```

**Shared dependencies (rare):**
```bash
# At root level
pnpm add -w package-name
```

### Building and Type Checking

```bash
# Build all apps
pnpm build

# Type check all apps
pnpm check-types
```

## 4. Technology Stack Summary

Your stack creates a **type-safe, full-stack application** with these key integrations:

### **Next.js (Frontend & Backend)**
- **Web app**: React-based frontend with App Router
- **Server app**: API routes hosting tRPC endpoints

### **tRPC Integration**
- **Server**: Defines type-safe API procedures in `apps/server/src/routers/`
- **Web**: Consumes APIs with full TypeScript inference via `@trpc/tanstack-react-query`
- **Native**: Shares the same tRPC client setup for consistent API access

### **Drizzle ORM + Neon PostgreSQL**
- **Schema**: Defined in `apps/server/src/db/schema/`
- **Database**: Neon PostgreSQL (serverless Postgres)
- **Migrations**: Managed through Drizzle Kit commands

### **Better Auth**
- **Unified authentication** across web and mobile
- **Email/password** authentication setup
- **Session management** with secure tokens

### **Turborepo Orchestration**
- **Parallel execution** of development servers
- **Dependency-aware builds** (server builds before web if needed)
- **Shared caching** for faster subsequent builds
- **Task pipelines** defined in `turbo.json`

### **Styling & UI**
- **Web**: Tailwind CSS + shadcn/ui components
- **Native**: NativeWind (Tailwind for React Native)

This setup gives you a **unified development experience** where you can share types, authentication, and business logic across web and mobile platforms while maintaining optimal performance and developer experience.
