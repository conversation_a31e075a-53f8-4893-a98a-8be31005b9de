Basic Requirements for the Clinical Decision Support System (CDSS) Project

1. Functional Requirements
1.1 Patient Data Management

Store basic patient details (name, age, gender, optional contact info).
Support CRUD operations (Create, Read, Update, Delete) for patient records, accessible only via the web app for doctors.
Assign a unique patient ID to link with ECG data.

1.2 Doctor Data Management

Store doctor details (name, organization email, contact info).
Support CRUD operations for doctor records, managed through the web app.
Define doctor roles for access control.

1.3 Authentication

Doctor Authentication (Web App):
Secure login for doctors using organization email (e.g., <EMAIL>).
Implement role-based access control (RBAC) to allow doctors to access assigned patients’ data, ECG streams, and alerts.


Patient Authentication (Mobile App):
Secure login for patients using credentials (e.g., username/password).
Restrict patients to viewing only their own details and (optionally) ECG data or diagnosis results.


Use secure authentication methods (e.g., JWT tokens) for both web and mobile apps.

1.4 ECG Data Handling

Store ECG data linked to patient IDs in a database.
Retrieve historical ECG data for doctors via the web app.
Simulate real-time ECG streaming from a dataset (e.g., PhysioNet) for display in the web app.

1.5 Real-Time ECG Classification

Process ECG signals in 10-second segments.
Use a pre-trained machine learning model to classify segments and detect abnormalities (e.g., arrhythmias).
Generate diagnosis results for display in the web app and optional sharing with patients via the mobile app.

1.6 Alert System

Send real-time alerts to doctors for abnormal ECG classifications via:
In-app notifications in the web app.
Email using the doctor’s organization email.
SMS using Twilio.


Allow doctors to view and acknowledge alerts in the web app.
Optionally notify patients of shared diagnosis results via the mobile app.

1.7 Web Application (Doctor Usage)

Doctor Dashboard:
Display a list of assigned patients with basic details (name, age, gender).
Show real-time ECG streams as a waveform for selected patients.
Present alerts for abnormal classifications with options to acknowledge or take action.


Patient Management Interface:
View and edit patient records.
Access historical ECG data and diagnosis results.


Responsive Design:
Ensure usability on desktop and tablet browsers.



1.8 Mobile Application (Patient Usage)

Patient Dashboard:
Display personal details (name, age, gender).
Optionally show ECG data or diagnosis results shared by doctors.


Notifications:
Receive notifications for shared diagnosis results (if enabled).


Platform Support:
Build for iOS and Android using a cross-platform framework (e.g., React Native).


Simple UI:
Provide a lightweight, user-friendly interface for patients.



1.9 Backend Services

API Service:
Provide APIs for patient and doctor data management (CRUD operations).
Support authentication for both web and mobile apps.


Real-Time Streaming Service:
Stream ECG data and classification results to the web app in real-time.
Optionally share diagnosis results with the mobile app via API.


Database Integration:
Store and retrieve patient, doctor, ECG, and alert data.




2. Non-Functional Requirements

Performance:
Achieve low-latency ECG streaming (<100ms updates) for the web app to support 360 Hz sampling rate.
Ensure efficient classification to deliver real-time results.


Security:
Encrypt patient and ECG data at rest and in transit.
Use HTTPS for web app and API communications; secure protocols for mobile app.
Comply with basic healthcare data regulations (e.g., HIPAA principles).


Scalability:
Support multiple concurrent doctors and patients.
Handle growing ECG data volumes efficiently.


Usability:
Provide an intuitive, responsive web interface for doctors.
Ensure a simple, accessible mobile app for patients on iOS and Android.


Reliability:
Maintain high availability for real-time ECG monitoring and alerts in the web app.
Implement error handling for streaming, API, and mobile app failures.




